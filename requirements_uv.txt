# YOLOv8 TensorFlow Lite Export Requirements (UV + Python 3.11)
# Install with: uv pip install -r requirements_uv.txt

# Core dependencies - install in order for best compatibility
numpy>=1.21.0,<2.0
tensorflow==2.15.0
ultralytics>=8.0.0

# TensorFlow Lite export dependencies
onnx>=1.12.0
sng4onnx>=1.0.1
onnx_graphsurgeon>=0.3.26
onnx2tf>=1.15.0
tf_keras

# Additional utilities
loguru>=0.7.0
pillow>=9.0.0
opencv-python>=4.8.0
