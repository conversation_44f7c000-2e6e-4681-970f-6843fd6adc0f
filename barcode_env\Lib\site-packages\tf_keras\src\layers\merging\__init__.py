# Copyright 2015 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Keras merging layers."""


# Merging functions.
# Merging layers.
from tf_keras.src.layers.merging.add import Add
from tf_keras.src.layers.merging.add import add
from tf_keras.src.layers.merging.average import Average
from tf_keras.src.layers.merging.average import average
from tf_keras.src.layers.merging.concatenate import Concatenate
from tf_keras.src.layers.merging.concatenate import concatenate
from tf_keras.src.layers.merging.dot import Dot
from tf_keras.src.layers.merging.dot import dot
from tf_keras.src.layers.merging.maximum import Maximum
from tf_keras.src.layers.merging.maximum import maximum
from tf_keras.src.layers.merging.minimum import Minimum
from tf_keras.src.layers.merging.minimum import minimum
from tf_keras.src.layers.merging.multiply import Multiply
from tf_keras.src.layers.merging.multiply import multiply
from tf_keras.src.layers.merging.subtract import Subtract
from tf_keras.src.layers.merging.subtract import subtract

