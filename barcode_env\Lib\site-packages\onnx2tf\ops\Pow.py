import random
random.seed(0)
import numpy as np
np.random.seed(0)
import tensorflow as tf
import onnx_graphsurgeon as gs
from onnx2tf.utils.common_functions import (
    get_constant_or_variable,
    print_node_info,
    inverted_operation_enable_disable,
    make_tf_node_info,
    is_integer_num,
    get_replacement_parameter,
    pre_process_transpose,
    post_process_transpose,
)
from onnx2tf.utils.enums import NUMPY_DTYPES_TO_TF_DTYPES


@print_node_info
@inverted_operation_enable_disable
@get_replacement_parameter
def make_node(
    *,
    graph_node: gs.Node,
    tf_layers_dict: dict,
    **kwargs: dict,
):
    """Pow

    Parameters
    ----------
    graph_node: gs.Node
        graph_surgeon Node

    tf_layers_dict: dict
        optype, shape, dtype, tensorflow graph
    """
    before_op_output_shape_trans_1 = \
        tf_layers_dict.get(graph_node.inputs[0].name, {}).get('before_op_output_shape_trans', True)
    before_op_output_shape_trans_2 = \
        tf_layers_dict.get(graph_node.inputs[1].name, {}).get('before_op_output_shape_trans', True)
    before_op_output_shape_trans = \
        before_op_output_shape_trans_1 \
        and before_op_output_shape_trans_2

    graph_node_input_1 = get_constant_or_variable(
        graph_node.inputs[0],
        before_op_output_shape_trans,
    )
    graph_node_input_2 = get_constant_or_variable(
        graph_node.inputs[1],
        before_op_output_shape_trans,
    )
    graph_node_output: gs.Variable = graph_node.outputs[0]
    shape = graph_node_output.shape
    output_dtype = graph_node_output.dtype

    # Preserving Graph Structure (Dict)
    tf_layers_dict[graph_node_output.name] = {
        'optype': graph_node.op,
        'shape': shape,
        'dtype': output_dtype,
        'nhwc': tf_layers_dict[graph_node_input_1.name]['nhwc'] \
            if isinstance(graph_node_input_1, gs.Variable) \
                and 'nhwc' in tf_layers_dict[graph_node_input_1.name].keys() else False
    }

    # Generation of TF OP
    input_tensor_1 = tf_layers_dict[graph_node_input_1.name]['tf_node'] \
        if isinstance(graph_node_input_1, gs.Variable) else graph_node_input_1
    input_tensor_2 = tf_layers_dict[graph_node_input_2.name]['tf_node'] \
        if isinstance(graph_node_input_2, gs.Variable) else graph_node_input_2

    # Pre-process transpose
    before_trans_shape = input_tensor_1.shape
    input_tensor_1 = pre_process_transpose(
        value_before_transpose=input_tensor_1,
        param_target='inputs',
        param_name=graph_node.inputs[0].name,
        **kwargs,
    )
    after_trans_shape = input_tensor_1.shape
    if 'nhwc' in tf_layers_dict[graph_node_output.name].keys() \
        and tf_layers_dict[graph_node_output.name]['nhwc'] == True \
        and before_trans_shape != after_trans_shape:
        tf_layers_dict[graph_node_output.name].pop('nhwc')

    input_tensor_2 = pre_process_transpose(
        value_before_transpose=input_tensor_2,
        param_target='inputs',
        param_name=graph_node.inputs[1].name,
        **kwargs,
    )

    replace_power_to_pseudo_power = "power" in kwargs['replace_to_pseudo_operators'] \
                                    or "pow" in kwargs['replace_to_pseudo_operators']

    if not replace_power_to_pseudo_power:
        if isinstance(input_tensor_2, np.ndarray) and input_tensor_2.ndim == 0 and input_tensor_2 == 2 \
            or isinstance(input_tensor_2, np.ndarray) and input_tensor_2.ndim == 1 and np.squeeze(input_tensor_2) == 2 \
            or isinstance(input_tensor_2, float) and input_tensor_2 == 2 \
            or isinstance(input_tensor_2, int) and input_tensor_2 == 2:
            powed_tensor = \
                tf.math.multiply(
                    x=input_tensor_1,
                    y=input_tensor_1,
                    name=graph_node.name,
                )
        elif isinstance(input_tensor_2, np.ndarray) and input_tensor_2.ndim == 0 and input_tensor_2 == 3 \
            or isinstance(input_tensor_2, np.ndarray) and input_tensor_2.ndim == 1 and np.squeeze(input_tensor_2) == 3 \
            or isinstance(input_tensor_2, float) and input_tensor_2 == 3 \
            or isinstance(input_tensor_2, int) and input_tensor_2 == 3:
            powed_tensor = \
                tf.math.multiply(
                    x=tf.math.multiply(
                        x=input_tensor_1,
                        y=input_tensor_1,
                    ),
                    y=input_tensor_1,
                    name=graph_node.name,
                )
        else:
            powed_tensor = \
                tf.math.pow(
                    x=input_tensor_1,
                    y=input_tensor_2,
                    name=graph_node.name,
                )
    else:
        if is_integer_num(x=input_tensor_2):
            if isinstance(input_tensor_2, np.ndarray):
                pow = input_tensor_2.squeeze()
            pow = int(pow)
            powed_tensor = input_tensor_1
            for i in range(pow-1):
                powed_tensor = tf.math.multiply(
                    powed_tensor,
                    (input_tensor_1 * 1.0)
                )
        else:
            powed_tensor = tf.math.pow(
                x=input_tensor_1,
                y=input_tensor_2,
                name=graph_node.name,
            )
    to_dtype = NUMPY_DTYPES_TO_TF_DTYPES[output_dtype] \
        if isinstance(output_dtype, np.dtype) else output_dtype
    if to_dtype is None:
        to_dtype = input_tensor_1.dtype
    if to_dtype == tf.float16:
        to_dtype = input_tensor_1.dtype
    if powed_tensor.dtype != to_dtype:
        powed_tensor = tf.cast(
            powed_tensor,
            dtype=NUMPY_DTYPES_TO_TF_DTYPES[to_dtype] \
                if isinstance(to_dtype, np.dtype) else to_dtype
        )
    tf_layers_dict[graph_node_output.name]['tf_node'] = powed_tensor

    # Post-process transpose
    before_trans_shape = tf_layers_dict[graph_node_output.name]['tf_node'].shape
    tf_layers_dict[graph_node_output.name]['tf_node'] = post_process_transpose(
        value_before_transpose=tf_layers_dict[graph_node_output.name]['tf_node'],
        param_target='outputs',
        param_name=graph_node.outputs[0].name,
        **kwargs,
    )
    after_trans_shape = tf_layers_dict[graph_node_output.name]['tf_node'].shape
    if 'nhwc' in tf_layers_dict[graph_node_output.name].keys() \
        and tf_layers_dict[graph_node_output.name]['nhwc'] == True \
        and before_trans_shape != after_trans_shape:
        tf_layers_dict[graph_node_output.name].pop('nhwc')

    # Generation of Debug Info
    tf_layers_dict[graph_node_output.name]['tf_node_info'] = \
        make_tf_node_info(
            node_info={
                'tf_op_type': tf.math.pow,
                'tf_inputs': {
                    'x': input_tensor_1,
                    'y': input_tensor_2,
                },
                'tf_outputs': {
                    'output': tf_layers_dict[graph_node_output.name]['tf_node'],
                },
            }
        )
