# YOLOv8 TensorFlow Lite Export Requirements (WSL)
# Compatible with Python 3.9-3.12
# Install with: pip install -r requirements_tflite.txt

# Core dependencies with compatible version constraints
numpy>=1.21.0,<2.0          # Critical: TensorFlow requires NumPy 1.x
tensorflow>=2.10.0,<2.16.0  # TensorFlow with broader compatibility
ultralytics>=8.0.0          # YOLOv8 framework

# TensorFlow Lite export dependencies
onnx2tf>=1.15.0             # ONNX to TensorFlow converter (relaxed version)
tf_keras                    # TensorFlow Keras (let pip choose compatible version)
sng4onnx>=1.0.1             # ONNX graph manipulation
onnx_graphsurgeon>=0.3.26   # NVIDIA ONNX GraphSurgeon
onnx>=1.12.0                # ONNX format support

# Additional utilities
loguru>=0.7.0               # Enhanced logging
pillow>=9.0.0               # Image processing
opencv-python>=4.8.0        # Computer vision utilities
