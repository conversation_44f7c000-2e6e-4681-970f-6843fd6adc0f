#!/bin/bash
# Activation script for TensorFlow Lite export environment
# Usage: source activate_tflite_env.sh

echo "🐍 Activating Python 3.11 TensorFlow Lite environment..."

# Check if we're in the right directory
if [ ! -d "venv_tflite" ]; then
    echo "❌ Virtual environment 'venv_tflite' not found in current directory"
    echo "   Make sure you're in the barcode_detector project directory"
    return 1
fi

# Activate virtual environment
source venv_tflite/bin/activate

# Verify environment
echo "✅ Virtual environment activated"
echo "🐍 Python version: $(python --version)"
echo "📦 Pip version: $(pip --version)"

# Quick dependency check
python -c "
try:
    import tensorflow as tf
    import ultralytics
    print('✅ TensorFlow:', tf.__version__)
    print('✅ Ultralytics:', ultralytics.__version__)
    print('🚀 Environment ready for TensorFlow Lite export!')
except ImportError as e:
    print('❌ Missing dependencies:', e)
    print('💡 Run: pip install -r requirements_tflite.txt')
" 2>/dev/null

echo ""
echo "🎯 Ready to export! Run: python export_tflite_only.py"
