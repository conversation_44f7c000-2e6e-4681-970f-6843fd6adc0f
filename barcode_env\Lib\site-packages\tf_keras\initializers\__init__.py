"""AUTOGENERATED. DO NOT EDIT."""

from tf_keras.src.initializers import deserialize
from tf_keras.src.initializers import get
from tf_keras.src.initializers import serialize
from tf_keras.src.initializers.initializers import Constant
from tf_keras.src.initializers.initializers import Constant as constant
from tf_keras.src.initializers.initializers import GlorotNormal
from tf_keras.src.initializers.initializers import GlorotNormal as glorot_normal
from tf_keras.src.initializers.initializers import GlorotUniform
from tf_keras.src.initializers.initializers import GlorotUniform as glorot_uniform
from tf_keras.src.initializers.initializers import HeNormal
from tf_keras.src.initializers.initializers import HeNormal as he_normal
from tf_keras.src.initializers.initializers import HeUniform
from tf_keras.src.initializers.initializers import HeUniform as he_uniform
from tf_keras.src.initializers.initializers import Identity
from tf_keras.src.initializers.initializers import Identity as identity
from tf_keras.src.initializers.initializers import Initializer
from tf_keras.src.initializers.initializers import LecunNormal
from tf_keras.src.initializers.initializers import LecunNormal as lecun_normal
from tf_keras.src.initializers.initializers import LecunUniform
from tf_keras.src.initializers.initializers import LecunUniform as lecun_uniform
from tf_keras.src.initializers.initializers import Ones
from tf_keras.src.initializers.initializers import Ones as ones
from tf_keras.src.initializers.initializers import Orthogonal
from tf_keras.src.initializers.initializers import Orthogonal as orthogonal
from tf_keras.src.initializers.initializers import RandomNormal
from tf_keras.src.initializers.initializers import RandomNormal as random_normal
from tf_keras.src.initializers.initializers import RandomUniform
from tf_keras.src.initializers.initializers import RandomUniform as random_uniform
from tf_keras.src.initializers.initializers import TruncatedNormal
from tf_keras.src.initializers.initializers import TruncatedNormal as truncated_normal
from tf_keras.src.initializers.initializers import VarianceScaling
from tf_keras.src.initializers.initializers import VarianceScaling as variance_scaling
from tf_keras.src.initializers.initializers import Zeros
from tf_keras.src.initializers.initializers import Zeros as zeros
