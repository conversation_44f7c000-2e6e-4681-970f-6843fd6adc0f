// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2tensorrt/utils/trt_engine_instance.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto;
namespace tensorflow {
namespace tensorrt {
class TRTEngineInstance;
struct TRTEngineInstanceDefaultTypeInternal;
extern TRTEngineInstanceDefaultTypeInternal _TRTEngineInstance_default_instance_;
}  // namespace tensorrt
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tensorrt::TRTEngineInstance* Arena::CreateMaybeMessage<::tensorflow::tensorrt::TRTEngineInstance>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tensorrt {

// ===================================================================

class TRTEngineInstance final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tensorrt.TRTEngineInstance) */ {
 public:
  inline TRTEngineInstance() : TRTEngineInstance(nullptr) {}
  ~TRTEngineInstance() override;
  explicit PROTOBUF_CONSTEXPR TRTEngineInstance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TRTEngineInstance(const TRTEngineInstance& from);
  TRTEngineInstance(TRTEngineInstance&& from) noexcept
    : TRTEngineInstance() {
    *this = ::std::move(from);
  }

  inline TRTEngineInstance& operator=(const TRTEngineInstance& from) {
    CopyFrom(from);
    return *this;
  }
  inline TRTEngineInstance& operator=(TRTEngineInstance&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TRTEngineInstance& default_instance() {
    return *internal_default_instance();
  }
  static inline const TRTEngineInstance* internal_default_instance() {
    return reinterpret_cast<const TRTEngineInstance*>(
               &_TRTEngineInstance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TRTEngineInstance& a, TRTEngineInstance& b) {
    a.Swap(&b);
  }
  inline void Swap(TRTEngineInstance* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TRTEngineInstance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TRTEngineInstance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TRTEngineInstance>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TRTEngineInstance& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TRTEngineInstance& from) {
    TRTEngineInstance::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TRTEngineInstance* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tensorrt.TRTEngineInstance";
  }
  protected:
  explicit TRTEngineInstance(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputShapesFieldNumber = 1,
    kSerializedEngineFieldNumber = 2,
  };
  // repeated .tensorflow.TensorShapeProto input_shapes = 1;
  int input_shapes_size() const;
  private:
  int _internal_input_shapes_size() const;
  public:
  void clear_input_shapes();
  ::tensorflow::TensorShapeProto* mutable_input_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_input_shapes();
  private:
  const ::tensorflow::TensorShapeProto& _internal_input_shapes(int index) const;
  ::tensorflow::TensorShapeProto* _internal_add_input_shapes();
  public:
  const ::tensorflow::TensorShapeProto& input_shapes(int index) const;
  ::tensorflow::TensorShapeProto* add_input_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      input_shapes() const;

  // bytes serialized_engine = 2;
  void clear_serialized_engine();
  const std::string& serialized_engine() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serialized_engine(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serialized_engine();
  PROTOBUF_NODISCARD std::string* release_serialized_engine();
  void set_allocated_serialized_engine(std::string* serialized_engine);
  private:
  const std::string& _internal_serialized_engine() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serialized_engine(const std::string& value);
  std::string* _internal_mutable_serialized_engine();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tensorrt.TRTEngineInstance)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > input_shapes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serialized_engine_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TRTEngineInstance

// repeated .tensorflow.TensorShapeProto input_shapes = 1;
inline int TRTEngineInstance::_internal_input_shapes_size() const {
  return _impl_.input_shapes_.size();
}
inline int TRTEngineInstance::input_shapes_size() const {
  return _internal_input_shapes_size();
}
inline ::tensorflow::TensorShapeProto* TRTEngineInstance::mutable_input_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tensorrt.TRTEngineInstance.input_shapes)
  return _impl_.input_shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
TRTEngineInstance::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tensorrt.TRTEngineInstance.input_shapes)
  return &_impl_.input_shapes_;
}
inline const ::tensorflow::TensorShapeProto& TRTEngineInstance::_internal_input_shapes(int index) const {
  return _impl_.input_shapes_.Get(index);
}
inline const ::tensorflow::TensorShapeProto& TRTEngineInstance::input_shapes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tensorrt.TRTEngineInstance.input_shapes)
  return _internal_input_shapes(index);
}
inline ::tensorflow::TensorShapeProto* TRTEngineInstance::_internal_add_input_shapes() {
  return _impl_.input_shapes_.Add();
}
inline ::tensorflow::TensorShapeProto* TRTEngineInstance::add_input_shapes() {
  ::tensorflow::TensorShapeProto* _add = _internal_add_input_shapes();
  // @@protoc_insertion_point(field_add:tensorflow.tensorrt.TRTEngineInstance.input_shapes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
TRTEngineInstance::input_shapes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tensorrt.TRTEngineInstance.input_shapes)
  return _impl_.input_shapes_;
}

// bytes serialized_engine = 2;
inline void TRTEngineInstance::clear_serialized_engine() {
  _impl_.serialized_engine_.ClearToEmpty();
}
inline const std::string& TRTEngineInstance::serialized_engine() const {
  // @@protoc_insertion_point(field_get:tensorflow.tensorrt.TRTEngineInstance.serialized_engine)
  return _internal_serialized_engine();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TRTEngineInstance::set_serialized_engine(ArgT0&& arg0, ArgT... args) {
 
 _impl_.serialized_engine_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tensorrt.TRTEngineInstance.serialized_engine)
}
inline std::string* TRTEngineInstance::mutable_serialized_engine() {
  std::string* _s = _internal_mutable_serialized_engine();
  // @@protoc_insertion_point(field_mutable:tensorflow.tensorrt.TRTEngineInstance.serialized_engine)
  return _s;
}
inline const std::string& TRTEngineInstance::_internal_serialized_engine() const {
  return _impl_.serialized_engine_.Get();
}
inline void TRTEngineInstance::_internal_set_serialized_engine(const std::string& value) {
  
  _impl_.serialized_engine_.Set(value, GetArenaForAllocation());
}
inline std::string* TRTEngineInstance::_internal_mutable_serialized_engine() {
  
  return _impl_.serialized_engine_.Mutable(GetArenaForAllocation());
}
inline std::string* TRTEngineInstance::release_serialized_engine() {
  // @@protoc_insertion_point(field_release:tensorflow.tensorrt.TRTEngineInstance.serialized_engine)
  return _impl_.serialized_engine_.Release();
}
inline void TRTEngineInstance::set_allocated_serialized_engine(std::string* serialized_engine) {
  if (serialized_engine != nullptr) {
    
  } else {
    
  }
  _impl_.serialized_engine_.SetAllocated(serialized_engine, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.serialized_engine_.IsDefault()) {
    _impl_.serialized_engine_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tensorrt.TRTEngineInstance.serialized_engine)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorrt
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2tensorrt_2futils_2ftrt_5fengine_5finstance_2eproto
