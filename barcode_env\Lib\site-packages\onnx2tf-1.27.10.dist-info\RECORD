../../Scripts/onnx2tf.exe,sha256=0SuOOU62gBjZZFqF5jfQZACFyFNhMc7c4VWEHUSyoSk,40967
onnx2tf-1.27.10.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
onnx2tf-1.27.10.dist-info/METADATA,sha256=D-vbk2OeCeu06NZYvAQNJaoFexmJDBnEAwrg8D52WLY,149173
onnx2tf-1.27.10.dist-info/RECORD,,
onnx2tf-1.27.10.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnx2tf-1.27.10.dist-info/WHEEL,sha256=zaaOINJESkSfm_4HQVc5ssNzHCPXhJm0kEUakpsEHaU,91
onnx2tf-1.27.10.dist-info/entry_points.txt,sha256=gDPK8ToCFPKMvm8jr9xrGOkXtORJJVh4736fBEKO5k0,41
onnx2tf-1.27.10.dist-info/licenses/LICENSE,sha256=5v_Kxihy8i6mzHVl349ikSREaIdsl9YeUnX1KBDLD2w,1070
onnx2tf-1.27.10.dist-info/licenses/LICENSE_onnx-tensorflow,sha256=gK4GtS9S5YcyINu6uuNNWdo-kBClyEM4MFLFGiNTeRM,11231
onnx2tf-1.27.10.dist-info/top_level.txt,sha256=WgfPiEy3f6vZ_FOpAIEA2CF3TCx1eYrhGw93Ih6b9Fw,8
onnx2tf/__init__.py,sha256=XUWhuuMFDDXP_g52JLtZhc-ADNltAchMMkJMSLVGSTk,67
onnx2tf/__main__.py,sha256=2RSCQ7d4lc6CwD-rlGn9UicPFg-P5du7ZD_yh-kuBEU,57
onnx2tf/onnx2tf.py,sha256=_PB395J0GPgruBNqFzf_45-LR5boe3avZzoQIdnoSOY,126240
onnx2tf/ops/Abs.py,sha256=V7btmCG_ZvK_qJovUsguq0ZMJ349mhNQ4FHSgzP_Yuo,4029
onnx2tf/ops/Acos.py,sha256=Fo8YkFKuWq8Fi2xUrBdKcAH1yJ8r5pjSD0wgLttTNdk,4003
onnx2tf/ops/Acosh.py,sha256=ATQj2cT5JS_mTfXi0kXqJ1yzSZu5J0zHA5VjV3j7uKY,3588
onnx2tf/ops/Add.py,sha256=pgJTnV1wZZk3mRaVxxezVkArfmlqlk74DCMZDm6VRJc,12295
onnx2tf/ops/And.py,sha256=_ubtWa0r8-60x__pS7MEMil1DfBqxiUsk66yRCYS4KY,4591
onnx2tf/ops/ArgMax.py,sha256=F3PV4EchYQgH1GATJybVGnmY9sGvZkgxCHbNCue9Qns,7278
onnx2tf/ops/ArgMin.py,sha256=32r7I8AYLQOKTPOOPX1AZwiPnQfkrFB0Le16vdJ1yBs,4225
onnx2tf/ops/Asin.py,sha256=2djUjTaOzXM6t4Qb-EEMZY-pm1rJl24cgcrep2i_6aQ,4003
onnx2tf/ops/Asinh.py,sha256=74ZzTEkpxZY4CGfJT2JJU-SHXYL5KZeUkWY2v7hsMMw,3588
onnx2tf/ops/Atan.py,sha256=D24XDMxEwXFtJheQAr3V3IWOUOc6Q5M0-b_83bmGGMM,3981
onnx2tf/ops/Atanh.py,sha256=VsUYopBWWPoo4gta1_aqvUL6NrVXuVkGid4SqDqYJ9Q,3588
onnx2tf/ops/AveragePool.py,sha256=X2uMuo1CnPBpeqXbCkG-hMBmmUFGry-Xu4EXLg2aoIw,17297
onnx2tf/ops/BatchNormalization.py,sha256=_hlf2-5-j3MCJHEoE2oMNQ8YhCm7ad9h2fwPpTo3i7g,26624
onnx2tf/ops/Bernoulli.py,sha256=PM0xS0n1q4bnT_9PnbcKW8_Qj8dJYYBQR8kb2X-wIp4,3670
onnx2tf/ops/BitShift.py,sha256=a28_E9hwA8yfjvtsrSKCZCeeMPB5RBQbjB3cmaNGN6k,3861
onnx2tf/ops/Cast.py,sha256=M0LRClHPgZ_8NubwME6ipKrAqcY9aKC5ihQXCkTkNkM,4601
onnx2tf/ops/Ceil.py,sha256=0-jaueltpQSwpOIDUmy9DdTy98qN-XimYu5cHVPnUIs,3586
onnx2tf/ops/Celu.py,sha256=9g7WNKo4G_jMtUXcoOfpNdLYqEsuyXLPkkyQZxDuL4U,3853
onnx2tf/ops/Clip.py,sha256=K3Pgt9BXl5_rzg6s-kPFmwElL5COsvolRY1BUTo7UWw,8753
onnx2tf/ops/Col2Im.py,sha256=MDqck00gURrbsroJAgUDkxmdsGyE9v2ez4NKdvdq5IY,7514
onnx2tf/ops/Compress.py,sha256=NvDGr9gCNl-8YG41xDBfe3UvhRP03K-ktdtY_MoytBc,3667
onnx2tf/ops/Concat.py,sha256=CKfJbiAwP7h9sFFVyueHJCbwMkUo3NXqkTuRc8v7Tw8,31215
onnx2tf/ops/ConcatFromSequence.py,sha256=z8pNmGQRGq9cxWORW330NZS_0zsmhFudLswMyPn8AXU,3086
onnx2tf/ops/Constant.py,sha256=BNZLzNI4rK9kXgVWwD-2RFsDsH7mMy7AY2JSgTNXIWk,10696
onnx2tf/ops/ConstantOfShape.py,sha256=6eYm-niow-6fHVEyNyi81BdrVe3IbcdazCp2nySWExA,2331
onnx2tf/ops/Conv.py,sha256=jcBIgZY4IXxVnlNNmkGhIVbsE6UWmA7xfwe9Jw3J1G8,37881
onnx2tf/ops/ConvInteger.py,sha256=UVHy1de6uCLM7IXP4vpzPz2pN2ej278lGBiqTPNdXFA,26664
onnx2tf/ops/ConvTranspose.py,sha256=C7CR6m3kz0MtUBdtWrrKWZbZL7tJpGXl7Nkn3DRiEaA,15410
onnx2tf/ops/Cos.py,sha256=0v5ZJZRzrswVEObyxf4f0RvnWMWZA4uCEdoeq_VE31s,3608
onnx2tf/ops/Cosh.py,sha256=-L3QkQtiVBJIv1sSxbXtetVIwgI_2T4WC1O4t2aJ8Gc,3585
onnx2tf/ops/CumSum.py,sha256=SYKmD5r9Cm9gsCkJPNFoHigvvBO1PmRYRrVmn1HE78o,3954
onnx2tf/ops/DepthToSpace.py,sha256=BiyBZ88dmXQAkZ5Jc-Ddo-5Kn8dRYCnoik_XnOFzqXc,14449
onnx2tf/ops/DequantizeLinear.py,sha256=cNbGw4ITg_BsrXYkSb7fD05XEkQgz7v__-StQtvIvB4,5220
onnx2tf/ops/Det.py,sha256=kxuHkpv_KNHkof0uBv2RLtr3G1uA76MFHyCiCYCBXkw,3590
onnx2tf/ops/Div.py,sha256=NyAsvCxI41hyBX_kiCEILHY6QQkas_o4wRY8zkDUiwk,16248
onnx2tf/ops/Dropout.py,sha256=KZKVqlnbq875awsNvJaQRvkO3XgqxeAmjbikXymRCtA,5860
onnx2tf/ops/DynamicQuantizeLinear.py,sha256=UGmN2nXBBQHXcNlorEQfnKDnnoOadt4TNzXox-Xki2U,4759
onnx2tf/ops/Einsum.py,sha256=YBw0JmSglOVVje80RqmqIjgsc7V5SnYS6s1Ysa2NUPA,12369
onnx2tf/ops/Elu.py,sha256=VDd5cKc1h-8nd0bVwWR_CkgfomrBl4NMbjRtAvkoNks,4025
onnx2tf/ops/Equal.py,sha256=ni0gf7nJex8S-oG61bnHc_xn8LuMits3gM6IzGNT65w,4579
onnx2tf/ops/Erf.py,sha256=ayvSp8Pr9h-VYuIiMorwOC0r9aQ4i4S1Uvaho9R6PYo,4962
onnx2tf/ops/Exp.py,sha256=MM_Osse7UbJgld2u0fGMcjniJCs40uDztuOodVUqWMU,3583
onnx2tf/ops/Expand.py,sha256=MGsby7IhTqKv1K-1INQPHcI5l3Wx-jjJ1FnulpCZsag,14761
onnx2tf/ops/EyeLike.py,sha256=VHRlr_WpIGVpZSqfjN7zWQF6XT2KjNVJnjVccxB4P6U,5877
onnx2tf/ops/Flatten.py,sha256=q5mhKynr3-HVEVih-vcFiFj26Whqi0WSSNn6ckdr0Ac,6556
onnx2tf/ops/Floor.py,sha256=8izJrNmw8wNmjF_YabIpLs4jm82J-gKcyAQbwV7Yqpc,3589
onnx2tf/ops/FusedConv.py,sha256=gslI50V3yvt4l0mmodnyHFAu0cORx1J_ZL5cE0rZ8qs,4523
onnx2tf/ops/GRU.py,sha256=kBHiZlhlPIV2DQCoFYFHxCTwOATeguJy1MSfj2kxqDM,30732
onnx2tf/ops/Gather.py,sha256=ezsUTN8nWau4-kB696xjonlVWU6XQ6BjtyjSebt1EXg,15216
onnx2tf/ops/GatherElements.py,sha256=pR9EuOkYRBKPntmnj9DYpoBESc35EGv3RHfl0HCSmao,15026
onnx2tf/ops/GatherND.py,sha256=sdHaBeY2ycN9gRc_ahaZo2QI9XbV8PBthefm-JPiPnE,7642
onnx2tf/ops/Gelu.py,sha256=ms9oHnESOuiIPxl_8YU2WEnQo_BVKRPKo5UJsvsWyEA,4321
onnx2tf/ops/Gemm.py,sha256=8vGtXwx_V59JIDh3EBPuFVQSbIVql45zEHUlVGV3coU,7587
onnx2tf/ops/GlobalAveragePool.py,sha256=GrDDOywtO6peW79mBPmBJX9MrEU2PXso94xazAzx_xk,5704
onnx2tf/ops/GlobalLpPool.py,sha256=bzsQBuqKo5CI7HB-HlFC3m96swAarCsEnOlPMrmZVFM,5092
onnx2tf/ops/GlobalMaxPool.py,sha256=UAlMRlnJlgq9keEYYpqvmTruGJ4jeKIECArESbMlNOg,4985
onnx2tf/ops/Greater.py,sha256=fhMFF0fGt2c1W_rHCy0yKAXUYThLgBVnoFmCYLPD12Q,4585
onnx2tf/ops/GreaterOrEqual.py,sha256=sfNBveEyoU2oIlFILKlZ3jopeCnnPH2ij4J08QtIX8I,4604
onnx2tf/ops/GridSample.py,sha256=q0NFa0KeJxz8EmnZm3vEiMoQvk89oYT9zVogu1opVzY,30522
onnx2tf/ops/GroupNorm.py,sha256=zMjgkTDhb8OySDa4ZBg-45rWQQ5dy3wmqAY-Aj7izac,12026
onnx2tf/ops/HammingWindow.py,sha256=PY6NVvzutmFKB8UyJYl2LcwqzZGhRMg0jot96m0isCc,2891
onnx2tf/ops/HannWindow.py,sha256=vMvtn3JwjxUqPXTXdNzk3QjH87JFAEStwwEnIl_5jKY,2882
onnx2tf/ops/HardSigmoid.py,sha256=KDP_t-Z70sDsHMOYxyJ7ZNH31zqkrViOKYCcRG5NJHc,3662
onnx2tf/ops/HardSwish.py,sha256=nEng3LCDQYMZ4XhFZ7pXKGyRsM2_waowi8PlZt_f6Ck,3994
onnx2tf/ops/Hardmax.py,sha256=tiMch3Tuc8Rvy52hgGSfqfOVyXaEsnxYplRMy7vtpyA,4398
onnx2tf/ops/Identity.py,sha256=egudADqdhe4BiunYHUTh-AlDAkPpRESRT2eG0Q4rBts,2425
onnx2tf/ops/If.py,sha256=d1CD5R3-UuhO35XwLsVCtWhUvJJg6LKh5OKTmYrGBh8,6966
onnx2tf/ops/Input.py,sha256=aRZQ4uLWmMS3q317wZO68qqks8p3QDOINhTEObAhvvY,16225
onnx2tf/ops/InstanceNormalization.py,sha256=gUixsJ1105tt8UGwoLLdZ4V95GiZwzHm_jJMugqQ1yQ,11997
onnx2tf/ops/Inverse.py,sha256=YsRs0mpZg6dXWXnM1-UU5PcaUvrUqLmDDCNFpirXqp4,4595
onnx2tf/ops/IsInf.py,sha256=wTllsdaDdmfd2G9r92_M-RicK8ASWzWldTUJRP9o76s,3974
onnx2tf/ops/IsNaN.py,sha256=L2-fB6-EZR7KIccW9-srFZX1xbGafZu7S_6DWq0rJfM,3619
onnx2tf/ops/LRN.py,sha256=oFUJUH9Dofr_F-8dBGclp4bPwFD_VoSd1z29cPKF1gE,3174
onnx2tf/ops/LSTM.py,sha256=iPli4J362k29nwZlXZaUZ3-J9WUMAo8G1AvV67AxXEk,43922
onnx2tf/ops/LayerNormalization.py,sha256=698hPs6263OjJSXC8qTHXSPMeDF4cP42UPxyi08xh_k,11780
onnx2tf/ops/LeakyRelu.py,sha256=5bcgS70kpA50sTpsbXeXbTfPEDrn1KyeVpJrnAitv9c,4166
onnx2tf/ops/Less.py,sha256=YZp5u3cUMU9Gcv_JVqPSIeuaIzVlU0hKy0PnvE6BXFo,4576
onnx2tf/ops/LessOrEqual.py,sha256=9Lc8qaYUPVC6yZoQluNqcdHnvpUbfWBOI4Ow38RRAJo,4595
onnx2tf/ops/Log.py,sha256=UZebF3SGq85BnoPgYyN2j-zzFRp67fJnYPNyu33W55o,3582
onnx2tf/ops/LogSoftmax.py,sha256=j2nhYY7__8ViLFJVLA5tS98QEvGS1gTIW0QCdnZWUPQ,3923
onnx2tf/ops/LpNormalization.py,sha256=Uu15HgxFNXb6gNMgdTJyf0SLPaLbcbkOYqY_4hMBxNA,3153
onnx2tf/ops/MatMul.py,sha256=95HrWr3Dt6BLqx_zqm3WXBw_WzrWLObYVgz4K1yrhqE,19060
onnx2tf/ops/MatMulInteger.py,sha256=qHqzdJNI9SeJDbW8pR90baYCdGN6FdOez4hi9EzwXoc,6538
onnx2tf/ops/Max.py,sha256=w5nMciO_6ApYUobHuwMGuS3xhuza7eSvKDRhvMPgAuo,3256
onnx2tf/ops/MaxPool.py,sha256=_JC4eqBTh-qLkZCMG8RZhthRZ8D2d821zaFMWeGMEWc,15775
onnx2tf/ops/MaxUnpool.py,sha256=dGIEvC45rFuWoeG1S9j4sjEdEUqiWs_xdY5DZH6X7b4,5743
onnx2tf/ops/Mean.py,sha256=xfTjKpQntJB8uXAkgDLS77oLXy2yQh1Hlz0K2GltMh0,3132
onnx2tf/ops/MeanVarianceNormalization.py,sha256=Ne53jlDgAJZ9yhzKOWR-0LnjDdM-fg7DYmRytoP-4IA,3743
onnx2tf/ops/MelWeightMatrix.py,sha256=MyYFUTxz2wFVqNx3Dhlro0ktg9kxtEq8sGFmHICDZsI,5453
onnx2tf/ops/Min.py,sha256=dK3i115xYh6NusQtGfswEGYBg9MBc_g-edafLgvq4TQ,3356
onnx2tf/ops/Mish.py,sha256=LEg5MXBLLIzwxmsudC1zTA_yq7drVY_DMCB8lHBCA-8,3546
onnx2tf/ops/Mod.py,sha256=Y7kqCEOLqof4zVszJslQayt6COyU-MS5qKLHAYOyxmc,10023
onnx2tf/ops/Mul.py,sha256=0hOf2O8ktRpIi4eOMfLGdwKl-yACFyGO3nU_s_XXUIE,15986
onnx2tf/ops/Multinomial.py,sha256=0HQC76IA3AvRsUx9RS0S__nIfEmPuvIaDfSt8bns4FU,3158
onnx2tf/ops/Neg.py,sha256=vu2ExVXyGpggAM_DNPeZj9QFeUyqhn5XmJnDlPJFsQU,4219
onnx2tf/ops/NonMaxSuppression.py,sha256=nHeiX5eMGQAq_51KoljNZGlZddJ89Oe7Yfe33xLhl6M,15763
onnx2tf/ops/NonZero.py,sha256=2EYZFMNIejeqR2azHw0CT2mthiKuRPQepUafzeVE8Nk,2788
onnx2tf/ops/Not.py,sha256=wn3nThGf4gtpQdHjP7OX2xlhyaNQGeHifjZ18O5shhg,3599
onnx2tf/ops/OneHot.py,sha256=OThLm1MF1X75zx7gep_qdnRHsTRZX_tqZxjt6pAVi7E,6489
onnx2tf/ops/OptionalGetElement.py,sha256=Qg8Lix1_rdGtVWC-UNC91ekx3ztB-5_UtWkg67M2Z5E,3031
onnx2tf/ops/OptionalHasElement.py,sha256=fyZMPBWvLVohsm8J5FLMlYGBDr6HVq5lh9jL-yKp-tk,3031
onnx2tf/ops/Or.py,sha256=7gyUSgbEVVQBp2t3G93pZlHNn0ejJfZ3rbSDOnFgUi0,4586
onnx2tf/ops/PRelu.py,sha256=pHbsffhb2rLZPPb9NdKUT4f5-lC0TXmbZVafookXo90,6314
onnx2tf/ops/Pad.py,sha256=xZOkZK-53sXU-d0nADAjR1wOpKqfzHeJjTmzwon6G4A,11883
onnx2tf/ops/Pow.py,sha256=DZjrWQSyLw_BPXrKyoTqT9KJIxPfNxnYVcoTDBagDgM,7056
onnx2tf/ops/QLinearAdd.py,sha256=OssQI0pd8KXdnCC8urCPKP8bpcvSX0D76bS7q4-xMSY,5027
onnx2tf/ops/QLinearConcat.py,sha256=ZAde6h_OZ35gROmenr__pjrrni310TmOqjYUEu_gXIo,4530
onnx2tf/ops/QLinearConv.py,sha256=hgtvQdhy1Xsrdx-ThCT4L6v7zElWvgkt0-gJqdko8LY,12146
onnx2tf/ops/QLinearLeakyRelu.py,sha256=8Egl-ACtsnBrdcrm8LHzyWJEhcBi1dMp4lEBsqEGWA4,4521
onnx2tf/ops/QLinearMatMul.py,sha256=7OLXnwUoLAtpQRsK2w_RP_YXVwR0GQDyuLPGjVTnXOQ,5636
onnx2tf/ops/QLinearMul.py,sha256=QUqevMwVcDlSqAWlQ9ZTpNcvRlDXO1j3wWzEQZGEdq8,5056
onnx2tf/ops/QLinearSigmoid.py,sha256=pV18RrqC64ADQQMaxJIO1iwrjbf2hpUVcvBQfntiBJ0,3931
onnx2tf/ops/QLinearSoftmax.py,sha256=GtfT2gVH-V2j4NRqBbDFFfZWygp7TIjP662vo8k6dbU,4256
onnx2tf/ops/QuantizeLinear.py,sha256=tYKnsWZ_LBSMteVOTi8UV3F_dyTiE9tK8IeOmZjitVM,4563
onnx2tf/ops/RNN.py,sha256=55G5muM0BmJU9xIUU7hWsxhz5npisTfLJipR1w83ZDk,28143
onnx2tf/ops/RandomNormal.py,sha256=g1HvpScrHBOffqPT6yhSV1y2fNx7klruD6Vkolfl0to,2013
onnx2tf/ops/RandomNormalLike.py,sha256=BKguRxj48JhJ68Hce6xO8eE0OE-mTwnpymxBlV81ofw,2772
onnx2tf/ops/RandomUniform.py,sha256=3FIDoguM-eDVD0vAWt7XxYj3fhLLvubNDDWtNazh7es,2012
onnx2tf/ops/RandomUniformLike.py,sha256=CocvXoQkjeumITYuQGKF8TSTOX6xNKreZgQFopYqu0c,2771
onnx2tf/ops/Range.py,sha256=2Kcn0dDZ7T-zKeCyJUH3NRXGR7DhIcgmuAjRCWgBSbU,3340
onnx2tf/ops/Reciprocal.py,sha256=wRNioynAaaCnNZn183G2IPbEUTMtE5y95EwCoSHUNGE,3604
onnx2tf/ops/ReduceL1.py,sha256=O8u9MzkjETml9FOrLP98Ldr8BcvT-CBvFYfXU-Rl454,12341
onnx2tf/ops/ReduceL2.py,sha256=XFFzedagS7CN884VHk4TRm9Sxt7KD6n82g-aHJ29hbM,12285
onnx2tf/ops/ReduceLogSum.py,sha256=xa5CtTKBi-VJpSHWLTGZKcV4IjEw6RorkpM6r0ffuP8,12349
onnx2tf/ops/ReduceLogSumExp.py,sha256=xHBUGDyCHDfqlvYbJwg11-lAMundx_pj5K8pOQuOZwU,12357
onnx2tf/ops/ReduceMax.py,sha256=BQ6xZ4il1fZp5WEwtY6Kbm45fGTjC_M2S5FDiKKCT4U,12593
onnx2tf/ops/ReduceMean.py,sha256=I0SG4BLDriHMcIaW8c7GR3VwwgH3DW-0phOSETcj1Qw,12438
onnx2tf/ops/ReduceMin.py,sha256=uVhoE6gz2_6vrirqYvikMGx4k7DTsyGk6mkBn-dMX-A,12430
onnx2tf/ops/ReduceProd.py,sha256=I4qqmdfr4t8i1sinuDqZrvndpw6KrpN1B7sFcjRUI9g,12438
onnx2tf/ops/ReduceSum.py,sha256=8vdqR5Qv8ui783ywa0xVuiMm3MNwRzGOD_GYFpsgPmc,12393
onnx2tf/ops/ReduceSumSquare.py,sha256=X8OFxb5YRl6VU12i35f7Gwzegvf4mXO56dp69n2TZPs,12336
onnx2tf/ops/Relu.py,sha256=FoCRlHmG-xI3YCPbR_7UlRDbk0Juw6N722iULONkwW0,5627
onnx2tf/ops/Reshape.py,sha256=_oPKYi1uSwt_aVsuAt9v127lt0aR5jnhHTzxKNEKdx0,25626
onnx2tf/ops/Resize.py,sha256=nvcp8X7daMapWgmpCsjg4ajt8EdTQCzB6xbHCqmEQ9M,20053
onnx2tf/ops/ReverseSequence.py,sha256=W2w_fBCiUXsD28grIz4AHNIoMjYXx6b6HkgwJTVRxf8,3308
onnx2tf/ops/RoiAlign.py,sha256=XgLdaJgsI6KX0u8tnQlVsvbpZECZp_TSnEuGR7LTaeM,8360
onnx2tf/ops/Round.py,sha256=OHdh1G2qgZe5OWlRc-OEOM4eYaA63LAoQ6hPmmUmR6o,3588
onnx2tf/ops/STFT.py,sha256=LDKN309_dBu4v9AYpz70uMJbNjRFiOte9O3wUL4bIJw,4463
onnx2tf/ops/ScaleAndTranslate.py,sha256=VQDDhSs9TyMLQy0mF7n8pZ2TuvoKY-Lhlzd7Inf4UdI,11989
onnx2tf/ops/Scatter.py,sha256=5_rTM60FPCq8unyNPDO-BZXcuz6w9Uyl2Xqx-zJTpgg,746
onnx2tf/ops/ScatterElements.py,sha256=7u9-_pjS_x3JQsBCVnQyu6sPfuGx2o9qAW_RSZszOTs,7585
onnx2tf/ops/ScatterND.py,sha256=Y949fYKSAvkPW1s-58P7suafnna9hDLoTg0UA8cs2Ag,9087
onnx2tf/ops/Selu.py,sha256=CD0SqQlTTe0chO7lebkrdfDFSk6Cg9zLhvrKomsSH4Y,3799
onnx2tf/ops/SequenceAt.py,sha256=jpjl9gVJFagtg223YY26I0pUUEgEFjJGvSZWwbo2-mQ,3278
onnx2tf/ops/SequenceConstruct.py,sha256=KKbnpnitdAky23WF_DS49ot7ZxVoqBEU2ChgYEcXshY,2639
onnx2tf/ops/SequenceEmpty.py,sha256=mkKwp6-ULxUphhrI2-k2mo9cvkDfgbJo54xs8t8Ynrc,1743
onnx2tf/ops/SequenceErase.py,sha256=dBBec2PsKqQ_d-c-8rq18c21OXlI3QtvnFb_Cu8pHtU,3371
onnx2tf/ops/SequenceInsert.py,sha256=P0YOLzrB0_I4VJLg1j7rZ29_dclq4VJsbSOHt5A7Mfk,4840
onnx2tf/ops/SequenceLength.py,sha256=ihBJq1Qs8AXULs-P1CF9cwxPr9PlBvTEaVJGH4nzmLE,2693
onnx2tf/ops/Shape.py,sha256=saYIH-dazHqjx0lNZ4O_kl2Oyvf0DkutEIoc00t9glI,11905
onnx2tf/ops/Shrink.py,sha256=qBZjBvd0TjiiY8Lxl4ZL1sKW_WmO2G-u-J7aoUuLQQE,4005
onnx2tf/ops/Sigmoid.py,sha256=tgXtntEGN3oFzYaW0j1TwwGLzZ-_2ax6un04gwpriTs,3617
onnx2tf/ops/Sign.py,sha256=rJNyo_YTLO5x4yoF_Z_wpaIX4dSOL-vdmKH0SbVDwJc,3585
onnx2tf/ops/Sin.py,sha256=jrv76uQPIfB7UdLGf42MOlRUPM6fQ3GR6BvSybpptFo,3608
onnx2tf/ops/Sinh.py,sha256=9zXIQWcZiZmu3RnQuQpW-PEgBLOKY51SY0OBu1B5eh8,3706
onnx2tf/ops/Size.py,sha256=vFD5eae9Jko3tHbBtydj2d3T3tbb4r0xua7OIH40p9M,2665
onnx2tf/ops/Slice.py,sha256=6V1r1Dugra5qhrByHH6aDf_0KfrPSpwJYkxTGO7H44M,25046
onnx2tf/ops/Softmax.py,sha256=CEnHcSm25v1QC4QVDg4fz1NooYY1v-Uq4GORd8dnnr8,14773
onnx2tf/ops/Softplus.py,sha256=R44YMo8G2Ig15jBO6T2VOI6RhpUmjD70qvSCXFylU-Q,3605
onnx2tf/ops/Softsign.py,sha256=2ZdKH3KVHZXDzyO7S8f-O_aqRugurbRxd1i2g_fwCos,3600
onnx2tf/ops/SpaceToDepth.py,sha256=rWtPQNm2rErYs20gQyz-tFYsImAIUBGtdvfMVkJg5bo,2809
onnx2tf/ops/Split.py,sha256=ukm7QZmSwYwUwGLbVGsOiCEB3YfrFMl0cozn1kwgCv0,10728
onnx2tf/ops/SplitToSequence.py,sha256=BS_JEd7DC7vuPfs5oRRW774mtlK--kqf9DJUalv-Agk,5062
onnx2tf/ops/Sqrt.py,sha256=-xE8Tk_6unSR56k9g3R46lML4Nht5kQwqJT0JYkn5ko,3585
onnx2tf/ops/Squeeze.py,sha256=FLIt2qjWh1IJyti1c4YHuepH2Fkxt40rnEKszzmwsnE,7980
onnx2tf/ops/StringNormalizer.py,sha256=lyjUfhvZiIUZhLptI0rW_xwpFBJ6XuhDCyvCKNh-ogA,5214
onnx2tf/ops/Sub.py,sha256=JCUWNmRLrwJEB8_0MPRTzmZ4KAV_HLXNivUd_jNqPQI,11012
onnx2tf/ops/Sum.py,sha256=wtI0SbGuNFxkLskBk68ZhOAg3XyrIx-9xGYy1GZCVSo,3073
onnx2tf/ops/Tan.py,sha256=Ncig8clGvY7GWshqxRDRdcxjcbf_HTKGdpDw5ValrKI,3582
onnx2tf/ops/Tanh.py,sha256=PIQUvxS_AIDufblC2vc573nse2UCRA9z5yWd7kB-51s,3585
onnx2tf/ops/ThresholdedRelu.py,sha256=ArF3uRH7jN8kdYYDNcivJgv9UTFl5aqqSH2Qu79j4sY,3769
onnx2tf/ops/Tile.py,sha256=xkprg6yTaykivcHFJ644opzVPctaeplu-Ed-OpS98Gg,12720
onnx2tf/ops/TopK.py,sha256=f6OG-DcMWneXwSjIkmY935SPyOMD5tMteHnlQHoJwQo,6348
onnx2tf/ops/Transpose.py,sha256=GwJFp7zVqodEsv5mGWviuFqeK93uVM7dbRQ1N8Ua1hg,9774
onnx2tf/ops/Trilu.py,sha256=uz2TgdErpo9GDp9n4PCe0_koIpNLgBoPCjv3A6VBTl8,4789
onnx2tf/ops/Unique.py,sha256=GUuOeTO9px22dHmlAn2SOmRHvBgSXo-SaPWm5rYUtPc,4084
onnx2tf/ops/Unsqueeze.py,sha256=uXZTFJYan_okpVU9jQ3ICNOhrz_jiwaY_R3wxA3UAuI,10749
onnx2tf/ops/Upsample.py,sha256=SX3N_wZHD8G5Z0PLcPgX1ZCzOdct-uTzxKeMhhzeBOw,5304
onnx2tf/ops/Where.py,sha256=MaCcY9g4mKZQqCgh4xtoylicP-xVu9f4boKiu_q9Ow8,7711
onnx2tf/ops/Xor.py,sha256=2ceqxHSI1Wtez_CIh8gFfvcu45Xboqfyp1iy3v2vuIs,4590
onnx2tf/ops/_Loop.py,sha256=eo5sNfrfOnKV6_I737AWsM5LJTY9DVOxQEvhanxtP4g,11322
onnx2tf/ops/__Loop.py,sha256=ClwMcbNS4hqUtW_pzwjMa9Cqg7ONvz9aplke55A0uJ0,19704
onnx2tf/ops/__init__.py,sha256=jnmUWWa-3dHzBZV9bmPzXu6eoz2dumJTzO7i8JdcgSM,25
onnx2tf/utils/__init__.py,sha256=E9FM9He68VIASDnYp-OrxvHFVn55GzWqw2OEkCqn1zg,27
onnx2tf/utils/common_functions.py,sha256=cvczLScMTAuyhLi1ljUeI2SRhACT0-OGBQLibg4yYdE,245521
onnx2tf/utils/enums.py,sha256=7c5TqetqB07VjyHoxJHfLgtqBqk9ZRyUF33fPOJR1IM,1649
onnx2tf/utils/logging.py,sha256=yUCmPuJ_XiUItM3sZMcaMO24JErkQy7zZwVTYWAuiKg,1982
