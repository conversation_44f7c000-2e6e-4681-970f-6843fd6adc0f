# Copyright 2016 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Keras Applications are premade architectures with pre-trained weights."""


from tf_keras.src.applications.convnext import ConvNeXtBase
from tf_keras.src.applications.convnext import ConvNeXtLarge
from tf_keras.src.applications.convnext import ConvNeXtSmall
from tf_keras.src.applications.convnext import ConvNeXtTiny
from tf_keras.src.applications.convnext import ConvNeXtXLarge
from tf_keras.src.applications.densenet import DenseNet121
from tf_keras.src.applications.densenet import DenseNet169
from tf_keras.src.applications.densenet import DenseNet201
from tf_keras.src.applications.efficientnet import EfficientNetB0
from tf_keras.src.applications.efficientnet import EfficientNetB1
from tf_keras.src.applications.efficientnet import EfficientNetB2
from tf_keras.src.applications.efficientnet import EfficientNetB3
from tf_keras.src.applications.efficientnet import EfficientNetB4
from tf_keras.src.applications.efficientnet import EfficientNetB5
from tf_keras.src.applications.efficientnet import EfficientNetB6
from tf_keras.src.applications.efficientnet import EfficientNetB7
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2B0
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2B1
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2B2
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2B3
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2L
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2M
from tf_keras.src.applications.efficientnet_v2 import EfficientNetV2S
from tf_keras.src.applications.inception_resnet_v2 import InceptionResNetV2
from tf_keras.src.applications.inception_v3 import InceptionV3
from tf_keras.src.applications.mobilenet import MobileNet
from tf_keras.src.applications.mobilenet_v2 import MobileNetV2
from tf_keras.src.applications.mobilenet_v3 import MobileNetV3Large
from tf_keras.src.applications.mobilenet_v3 import MobileNetV3Small
from tf_keras.src.applications.nasnet import NASNetLarge
from tf_keras.src.applications.nasnet import NASNetMobile
from tf_keras.src.applications.resnet import ResNet50
from tf_keras.src.applications.resnet import ResNet101
from tf_keras.src.applications.resnet import ResNet152
from tf_keras.src.applications.resnet_rs import ResNetRS50
from tf_keras.src.applications.resnet_rs import ResNetRS101
from tf_keras.src.applications.resnet_rs import ResNetRS152
from tf_keras.src.applications.resnet_rs import ResNetRS200
from tf_keras.src.applications.resnet_rs import ResNetRS270
from tf_keras.src.applications.resnet_rs import ResNetRS350
from tf_keras.src.applications.resnet_rs import ResNetRS420
from tf_keras.src.applications.resnet_v2 import ResNet50V2
from tf_keras.src.applications.resnet_v2 import ResNet101V2
from tf_keras.src.applications.resnet_v2 import ResNet152V2
from tf_keras.src.applications.vgg16 import VGG16
from tf_keras.src.applications.vgg19 import VGG19
from tf_keras.src.applications.xception import Xception

