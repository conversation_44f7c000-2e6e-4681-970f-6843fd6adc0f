#!/usr/bin/env python3
"""
Check TensorFlow Lite export dependencies
"""

def check_dependency(module_name, package_name=None):
    """Check if a module can be imported"""
    if package_name is None:
        package_name = module_name
    
    try:
        module = __import__(module_name)
        if hasattr(module, '__version__'):
            print(f"✅ {package_name}: {module.__version__}")
        else:
            print(f"✅ {package_name}: installed (no version info)")
        return True
    except ImportError as e:
        print(f"❌ {package_name}: NOT INSTALLED - {e}")
        return False

def main():
    print("🔍 Checking TensorFlow Lite export dependencies...\n")
    
    # Core dependencies
    dependencies = [
        ("ultralytics", "ultralytics"),
        ("tensorflow", "tensorflow"),
        ("onnx", "onnx"),
        ("onnx2tf", "onnx2tf"),
        ("tf_keras", "tf_keras"),
        ("sng4onnx", "sng4onnx"),
        ("onnx_graphsurgeon", "onnx_graphsurgeon"),
    ]
    
    missing = []
    for module, package in dependencies:
        if not check_dependency(module, package):
            missing.append(package)
    
    print(f"\n📊 Summary:")
    print(f"✅ Installed: {len(dependencies) - len(missing)}")
    print(f"❌ Missing: {len(missing)}")
    
    if missing:
        print(f"\n🔧 Missing packages: {', '.join(missing)}")
        print("\n💡 To install missing packages, run:")
        print(f"pip install {' '.join(missing)}")
    else:
        print("\n🎉 All dependencies are installed!")

if __name__ == "__main__":
    main()
